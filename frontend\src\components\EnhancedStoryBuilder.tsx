import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { BookOpen, Wand2, Save, Shuffle, Share2, Download, Upload, Trash2, Edit, Eye } from "lucide-react";
import { toast } from "sonner";
import { useLibrary } from "@/contexts/LibraryContext";
import { api } from "@/services/api";

interface Story {
  id: number;
  title: string;
  content: string;
  genre: string;
  keywords: string[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

const EnhancedStoryBuilder = () => {
  const { libraries, selectedLibrary } = useLibrary();
  
  // Story creation state
  const [selectedWords, setSelectedWords] = useState<string[]>([]);
  const [selectedGenre, setSelectedGenre] = useState("");
  const [storyTitle, setStoryTitle] = useState("");
  const [storyContent, setStoryContent] = useState("");
  const [customCharacter, setCustomCharacter] = useState("");
  const [customScenario, setCustomScenario] = useState("");
  const [isPublic, setIsPublic] = useState(false);
  
  // Story management state
  const [stories, setStories] = useState<Story[]>([]);
  const [selectedStory, setSelectedStory] = useState<Story | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  const genres = [
    { name: "Fantasy", icon: "🧙‍♂️", description: "Magic and mythical creatures" },
    { name: "Sci-Fi", icon: "🚀", description: "Futuristic technology and space" },
    { name: "Mystery", icon: "🔍", description: "Puzzles and detective work" },
    { name: "Adventure", icon: "🗺️", description: "Exploration and excitement" },
    { name: "Romance", icon: "💕", description: "Love and relationships" },
    { name: "Thriller", icon: "⚡", description: "Suspense and danger" }
  ];

  // Load stories on component mount
  useEffect(() => {
    loadStories();
  }, []);

  const loadStories = async () => {
    try {
      const response = await api.get('/stories');
      if (response.data.success) {
        setStories(response.data.data.stories);
      }
    } catch (error) {
      console.error('Failed to load stories:', error);
      toast.error('Failed to load stories');
    }
  };

  const getAvailableWords = () => {
    if (!selectedLibrary || !selectedLibrary.words) return { learned: [], unlearned: [] };
    
    const learned = selectedLibrary.words.filter(word => word.is_learned).map(word => word.word);
    const unlearned = selectedLibrary.words.filter(word => !word.is_learned).map(word => word.word);
    
    return { learned, unlearned };
  };

  const handleWordToggle = (word: string) => {
    setSelectedWords(prev => 
      prev.includes(word) 
        ? prev.filter(w => w !== word)
        : [...prev, word]
    );
  };

  const handleGenreSelect = (genre: string) => {
    setSelectedGenre(genre);
  };

  const handleGenerateStory = async () => {
    if (selectedWords.length === 0) {
      toast.error("Please select at least one word to include in your story");
      return;
    }
    
    if (!selectedGenre) {
      toast.error("Please select a genre for your story");
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/stories/generate', {
        words: selectedWords,
        genre: selectedGenre,
        character: customCharacter,
        scenario: customScenario
      });

      if (response.data.success) {
        setStoryContent(response.data.data.story);
        setStoryTitle(`${selectedGenre} Adventure`);
        toast.success("Story generated successfully!");
      }
    } catch (error) {
      console.error('Failed to generate story:', error);
      toast.error('Failed to generate story');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveStory = async () => {
    if (!storyTitle.trim() || !storyContent.trim()) {
      toast.error("Please provide both title and content for your story");
      return;
    }

    setLoading(true);
    try {
      const storyData = {
        title: storyTitle,
        content: storyContent,
        genre: selectedGenre,
        keywords: selectedWords,
        is_public: isPublic
      };

      let response;
      if (isEditing && selectedStory) {
        response = await api.put(`/stories/${selectedStory.id}`, storyData);
      } else {
        response = await api.post('/stories', storyData);
      }

      if (response.data.success) {
        toast.success(isEditing ? "Story updated successfully!" : "Story saved successfully!");
        loadStories();
        resetForm();
      }
    } catch (error) {
      console.error('Failed to save story:', error);
      toast.error('Failed to save story');
    } finally {
      setLoading(false);
    }
  };

  const handleEditStory = (story: Story) => {
    setSelectedStory(story);
    setStoryTitle(story.title);
    setStoryContent(story.content);
    setSelectedGenre(story.genre || "");
    setSelectedWords(story.keywords || []);
    setIsPublic(story.is_public);
    setIsEditing(true);
  };

  const handleDeleteStory = async (storyId: number) => {
    if (!confirm("Are you sure you want to delete this story?")) return;

    try {
      const response = await api.delete(`/stories/${storyId}`);
      if (response.data.success) {
        toast.success("Story deleted successfully!");
        loadStories();
        if (selectedStory?.id === storyId) {
          resetForm();
        }
      }
    } catch (error) {
      console.error('Failed to delete story:', error);
      toast.error('Failed to delete story');
    }
  };

  const resetForm = () => {
    setStoryTitle("");
    setStoryContent("");
    setSelectedWords([]);
    setSelectedGenre("");
    setCustomCharacter("");
    setCustomScenario("");
    setIsPublic(false);
    setIsEditing(false);
    setSelectedStory(null);
  };

  const highlightWords = (text: string) => {
    let highlightedText = text;
    selectedWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, `<mark class="bg-yellow-200 px-1 rounded">${word}</mark>`);
    });
    return highlightedText;
  };

  const exportStory = (story: Story) => {
    const content = `# ${story.title}\n\n**Genre:** ${story.genre}\n**Keywords:** ${story.keywords?.join(', ')}\n**Created:** ${new Date(story.created_at).toLocaleDateString()}\n\n${story.content}`;
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${story.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Story exported successfully!");
  };

  const availableWords = getAvailableWords();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">Story Builder</h1>
          <p className="text-gray-600">Create engaging stories using your vocabulary words</p>
        </div>

        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-2">
            <TabsTrigger value="create">Create Story</TabsTrigger>
            <TabsTrigger value="manage">My Stories</TabsTrigger>
          </TabsList>

          {/* Create Story Tab */}
          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Wand2 className="h-5 w-5" />
                  <span>{isEditing ? 'Edit Story' : 'Create Your Story'}</span>
                </CardTitle>
                <CardDescription>
                  {isEditing ? 'Update your story' : 'Follow the steps to generate a personalized story'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="words" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="words">1. Select Words</TabsTrigger>
                    <TabsTrigger value="settings">2. Story Settings</TabsTrigger>
                    <TabsTrigger value="generate">3. Write Story</TabsTrigger>
                  </TabsList>

                  <TabsContent value="words" className="space-y-6">
                    {!selectedLibrary ? (
                      <div className="text-center py-8">
                        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">Please select a library first to access your vocabulary words</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {availableWords.learned.length > 0 && (
                          <div>
                            <h3 className="text-lg font-semibold mb-3">Learned Words ({availableWords.learned.length})</h3>
                            <div className="flex flex-wrap gap-2">
                              {availableWords.learned.map((word) => (
                                <Badge
                                  key={word}
                                  variant={selectedWords.includes(word) ? "default" : "outline"}
                                  className="cursor-pointer hover:bg-blue-100 px-3 py-1 capitalize"
                                  onClick={() => handleWordToggle(word)}
                                >
                                  {word}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {availableWords.unlearned.length > 0 && (
                          <div>
                            <h3 className="text-lg font-semibold mb-3">Unlearned Words ({availableWords.unlearned.length})</h3>
                            <div className="flex flex-wrap gap-2">
                              {availableWords.unlearned.map((word) => (
                                <Badge
                                  key={word}
                                  variant={selectedWords.includes(word) ? "default" : "secondary"}
                                  className="cursor-pointer hover:bg-gray-100 px-3 py-1 capitalize"
                                  onClick={() => handleWordToggle(word)}
                                >
                                  {word}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {selectedWords.length > 0 && (
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <p className="text-sm font-medium text-blue-900 mb-2">Selected Words ({selectedWords.length}):</p>
                            <div className="flex flex-wrap gap-1">
                              {selectedWords.map((word) => (
                                <Badge key={word} variant="default" className="text-xs capitalize">
                                  {word}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="settings" className="space-y-6">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="story-title">Story Title</Label>
                        <Input
                          id="story-title"
                          placeholder="Enter your story title"
                          value={storyTitle}
                          onChange={(e) => setStoryTitle(e.target.value)}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-3">Choose Genre</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {genres.map((genre) => (
                            <Card
                              key={genre.name}
                              className={`cursor-pointer transition-all hover:shadow-md ${
                                selectedGenre === genre.name ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                              }`}
                              onClick={() => handleGenreSelect(genre.name)}
                            >
                              <CardContent className="p-4 text-center">
                                <div className="text-2xl mb-2">{genre.icon}</div>
                                <h4 className="font-medium">{genre.name}</h4>
                                <p className="text-xs text-gray-600 mt-1">{genre.description}</p>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="character">Custom Character (Optional)</Label>
                          <Input
                            id="character"
                            placeholder="e.g., A brave astronaut"
                            value={customCharacter}
                            onChange={(e) => setCustomCharacter(e.target.value)}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="scenario">Custom Scenario (Optional)</Label>
                          <Input
                            id="scenario"
                            placeholder="e.g., Lost in a mysterious forest"
                            value={customScenario}
                            onChange={(e) => setCustomScenario(e.target.value)}
                            className="mt-1"
                          />
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="public-story"
                          checked={isPublic}
                          onCheckedChange={setIsPublic}
                        />
                        <Label htmlFor="public-story">Make story public (others can view it)</Label>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="generate" className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Write Your Story</h3>
                        <div className="flex space-x-2">
                          <Button 
                            onClick={handleGenerateStory} 
                            disabled={loading || selectedWords.length === 0}
                            variant="outline"
                          >
                            <Wand2 className="h-4 w-4 mr-2" />
                            {loading ? 'Generating...' : 'Generate Story'}
                          </Button>
                          <Button onClick={handleSaveStory} disabled={loading}>
                            <Save className="h-4 w-4 mr-2" />
                            {loading ? 'Saving...' : isEditing ? 'Update Story' : 'Save Story'}
                          </Button>
                        </div>
                      </div>

                      <Textarea
                        placeholder="Your story content will appear here, or you can write your own..."
                        value={storyContent}
                        onChange={(e) => setStoryContent(e.target.value)}
                        className="min-h-[300px]"
                      />

                      {storyContent && selectedWords.length > 0 && (
                        <div className="space-y-4">
                          <h4 className="font-medium">Story Preview (with highlighted vocabulary)</h4>
                          <div 
                            className="p-4 bg-gray-50 rounded-lg prose prose-sm max-w-none"
                            dangerouslySetInnerHTML={{ __html: highlightWords(storyContent) }}
                          />
                        </div>
                      )}

                      {isEditing && (
                        <div className="flex justify-between">
                          <Button variant="outline" onClick={resetForm}>
                            Cancel Edit
                          </Button>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Manage Stories Tab */}
          <TabsContent value="manage" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-5 w-5" />
                    <span>My Stories ({stories.length})</span>
                  </div>
                  <Button onClick={loadStories} variant="outline" size="sm">
                    Refresh
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {stories.length === 0 ? (
                  <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">No stories created yet</p>
                    <p className="text-sm text-gray-500">Create your first story using the "Create Story" tab</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {stories.map((story) => (
                      <Card key={story.id} className="hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <CardTitle className="text-lg line-clamp-2">{story.title}</CardTitle>
                              <div className="flex items-center space-x-2 mt-2">
                                <Badge variant="outline" className="text-xs">
                                  {story.genre}
                                </Badge>
                                {story.is_public && (
                                  <Badge variant="secondary" className="text-xs">
                                    <Share2 className="h-3 w-3 mr-1" />
                                    Public
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-sm text-gray-600 line-clamp-3 mb-4">
                            {story.content.substring(0, 150)}...
                          </p>
                          
                          {story.keywords && story.keywords.length > 0 && (
                            <div className="mb-4">
                              <p className="text-xs text-gray-500 mb-1">Keywords:</p>
                              <div className="flex flex-wrap gap-1">
                                {story.keywords.slice(0, 3).map((keyword, index) => (
                                  <Badge key={index} variant="outline" className="text-xs capitalize">
                                    {keyword}
                                  </Badge>
                                ))}
                                {story.keywords.length > 3 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{story.keywords.length - 3} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}

                          <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                            <span>Created: {new Date(story.created_at).toLocaleDateString()}</span>
                            <span>{story.content.split(' ').length} words</span>
                          </div>

                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditStory(story)}
                              className="flex-1"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => exportStory(story)}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteStory(story.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default EnhancedStoryBuilder;
